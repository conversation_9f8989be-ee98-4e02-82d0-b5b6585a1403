package com.uni.touch.smpp.accept.service.handler;

import com.cloudhopper.smpp.PduAsyncResponse;
import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.impl.DefaultSmppSessionHandler;
import com.cloudhopper.smpp.pdu.*;
import com.cloudhopper.smpp.tlv.Tlv;
import com.cloudhopper.smpp.util.SmppUtil;
import com.uni.touch.smpp.accept.service.converter.SmppEncodingConverter;
import com.uni.touch.smpp.accept.service.entity.*;
import com.uni.touch.smpp.accept.service.limiter.SmppRateLimiter;
import com.uni.touch.smpp.accept.service.manager.SmppSegmentManager;
import com.uni.touch.smpp.accept.service.processor.SmppSubmitProcessor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * SMPP会话处理器
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
public class SmppSessionHandler extends DefaultSmppSessionHandler {

    private final Long sessionId;
    private final SmppAccountInfo account;
    private final SmppSegmentManager segmentManager;
    private final SmppRateLimiter rateLimiter;
    private final SmppSubmitProcessor submitProcessor;
    private final SmppEncodingConverter encodingConverter;

    public SmppSessionHandler(Long sessionId, SmppAccountInfo account,
                            SmppSegmentManager segmentManager,
                            SmppRateLimiter rateLimiter,
                            SmppSubmitProcessor submitProcessor,
                            SmppEncodingConverter encodingConverter) {
        this.sessionId = sessionId;
        this.account = account;
        this.segmentManager = segmentManager;
        this.rateLimiter = rateLimiter;
        this.submitProcessor = submitProcessor;
        this.encodingConverter = encodingConverter;
    }

    @Override
    public PduResponse firePduRequestReceived(PduRequest pduRequest) {
        log.debug("收到PDU请求 - sessionId: {}, systemId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId, account.getSystemId(), Integer.toHexString(pduRequest.getCommandId()),
                pduRequest.getSequenceNumber());

        try {
            switch (pduRequest.getCommandId()) {
                case SmppConstants.CMD_ID_SUBMIT_SM:
                    return handleSubmitSm((SubmitSm) pduRequest);

                case SmppConstants.CMD_ID_ENQUIRE_LINK:
                    return handleEnquireLink((EnquireLink) pduRequest);

                case SmppConstants.CMD_ID_UNBIND:
                    return handleUnbind((Unbind) pduRequest);

                default:
                    log.warn("不支持的PDU类型 - sessionId: {}, commandId: 0x{}",
                        sessionId, Integer.toHexString(pduRequest.getCommandId()));
                    PduResponse response = pduRequest.createResponse();
                    response.setCommandStatus(SmppConstants.STATUS_INVCMDID);
                    return response;
            }

        } catch (Exception e) {
            log.error("处理PDU请求异常 - sessionId: {}, commandId: 0x{}",
                    sessionId, Integer.toHexString(pduRequest.getCommandId()), e);

            PduResponse response = pduRequest.createResponse();
            response.setCommandStatus(SmppConstants.STATUS_SYSERR);
            return response;
        }
    }

    /**
     * 处理短信提交请求
     */
    private SubmitSmResp handleSubmitSm(SubmitSm submitSm) {
        try {
            String systemId = account.getSystemId();
            log.info("处理短信提交 - sessionId: {}, systemId: {}, destAddr: {}, sourceAddr: {}",
                    sessionId, systemId,
                    submitSm.getDestAddress().getAddress(),
                    submitSm.getSourceAddress().getAddress());

            // 1. 流控检查
            if (!checkRateLimit(systemId)) {
                log.warn("提交速度超限 - systemId: {}", systemId);
                return createErrorResponse(submitSm, SmppConstants.STATUS_THROTTLED);
            }

            // 2. 解析消息信息
            SmppMessageInfo messageInfo = parseMessageInfo(submitSm);
            if (messageInfo == null) {
                return createErrorResponse(submitSm, SmppConstants.STATUS_INVMSGLEN);
            }

            // 3. 根据消息类型处理
            return processMessage(submitSm, messageInfo);

        } catch (Exception e) {
            log.error("处理SubmitSm异常 - sessionId: {}", sessionId, e);
            return createErrorResponse(submitSm, SmppConstants.STATUS_SYSERR);
        }
    }

    /**
     * 处理心跳请求
     */
    private EnquireLinkResp handleEnquireLink(EnquireLink enquireLink) {
        log.debug("收到心跳请求 - sessionId: {}, systemId: {}", sessionId, account.getSystemId());
        return enquireLink.createResponse();
    }

    /**
     * 处理解绑请求
     */
    private UnbindResp handleUnbind(Unbind unbind) {
        log.info("收到解绑请求 - sessionId: {}, systemId: {}", sessionId, account.getSystemId());
        return unbind.createResponse();
    }

    @Override
    public void fireExpectedPduResponseReceived(PduAsyncResponse pduAsyncResponse) {
        log.debug("收到预期PDU响应 - sessionId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId, 
                Integer.toHexString(pduAsyncResponse.getResponse().getCommandId()),
                pduAsyncResponse.getResponse().getSequenceNumber());
        
        super.fireExpectedPduResponseReceived(pduAsyncResponse);
    }

    @Override
    public void fireUnexpectedPduResponseReceived(PduResponse pduResponse) {
        log.warn("收到意外PDU响应 - sessionId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId, 
                Integer.toHexString(pduResponse.getCommandId()),
                pduResponse.getSequenceNumber());
        
        super.fireUnexpectedPduResponseReceived(pduResponse);
    }

    @Override
    public void fireChannelUnexpectedlyClosed() {
        log.warn("会话通道意外关闭 - sessionId: {}", sessionId);
        super.fireChannelUnexpectedlyClosed();
    }

    @Override
    public void firePduRequestExpired(PduRequest pduRequest) {
        log.warn("PDU请求超时 - sessionId: {}, commandId: 0x{}, sequenceNumber: {}",
                sessionId,
                Integer.toHexString(pduRequest.getCommandId()),
                pduRequest.getSequenceNumber());

        super.firePduRequestExpired(pduRequest);
    }

    // ==================== 私有方法 ====================

    /**
     * 检查流控限制
     */
    private boolean checkRateLimit(String systemId) {
        return rateLimiter.getSubmitRateLimiter(systemId, account.getMaxSubmitSpeed())
                .tryAcquire(1, 100, TimeUnit.MILLISECONDS);
    }

    /**
     * 解析消息信息
     */
    private SmppMessageInfo parseMessageInfo(SubmitSm submitSm) {
        try {
            // 1. 检查SAR TLV标签（优先级最高）
            if (hasSarTlvTags(submitSm)) {
                SmppSarInfo sarInfo = parseSarInfo(submitSm);
                if (sarInfo != null && sarInfo.isValid()) {
                    return SmppMessageInfo.createSarSegmented(sarInfo, getDataCoding());
                }
            }

            // 2. 检查message_payload
            if (submitSm.hasOptionalParameter(SmppConstants.TAG_MESSAGE_PAYLOAD)) {
                Tlv payloadTlv = submitSm.getOptionalParameter(SmppConstants.TAG_MESSAGE_PAYLOAD);
                if (payloadTlv != null && payloadTlv.getValue() != null) {
                    String content = encodingConverter.decodeMessage(payloadTlv.getValue(), account.getEncoding());
                    return SmppMessageInfo.createMessagePayload(content, getDataCoding());
                }
            }

            // 3. 检查UDH
            if (SmppUtil.isUserDataHeaderIndicatorEnabled(submitSm.getEsmClass())) {
                SmppUdhInfo udhInfo = parseUdhInfo(submitSm);
                if (udhInfo != null && udhInfo.isValid()) {
                    return SmppMessageInfo.createUdhSegmented(udhInfo, getDataCoding());
                }
            }

            // 4. 普通消息
            byte[] messageBytes = submitSm.getShortMessage();
            if (messageBytes != null) {
                String content = encodingConverter.decodeMessage(messageBytes, account.getEncoding());
                return SmppMessageInfo.createRegular(content, getDataCoding());
            }

            return null;

        } catch (Exception e) {
            log.error("解析消息信息异常", e);
            return null;
        }
    }

    /**
     * 处理消息
     */
    private SubmitSmResp processMessage(SubmitSm submitSm, SmppMessageInfo messageInfo) {
        switch (messageInfo.getMessageType()) {
            case SAR_SEGMENTED:
                return handleSarSegmentedMessage(submitSm, messageInfo.getSarInfo());

            case UDH_SEGMENTED:
                return handleUdhSegmentedMessage(submitSm, messageInfo.getUdhInfo());

            case MESSAGE_PAYLOAD:
            case REGULAR:
                return handleCompleteMessage(submitSm, messageInfo.getContent());

            default:
                log.warn("未知消息类型 - systemId: {}", account.getSystemId());
                return createErrorResponse(submitSm, SmppConstants.STATUS_INVMSGLEN);
        }
    }

    /**
     * 处理SAR分段消息
     */
    private SubmitSmResp handleSarSegmentedMessage(SubmitSm submitSm, SmppSarInfo sarInfo) {
        log.debug("处理SAR分段消息 - systemId: {}, 参考号: {}, 分段: {}/{}",
                account.getSystemId(), sarInfo.getRefNum(),
                sarInfo.getSegmentSeq(), sarInfo.getTotalSegments());

        // 处理分段重组
        SegmentReassembleInfo reassembled =
            segmentManager.processSarSegment(submitSm, account.getSystemId(), sarInfo);

        if (reassembled != null) {
            // 重组完成，提交完整消息
            return handleCompleteMessage(reassembled.getOriginalSubmitSm(), reassembled.getCompleteMessage());
        } else {
            // 分段缓存中，返回成功响应
            return createSuccessResponse(submitSm, generateMessageId());
        }
    }

    /**
     * 处理UDH分段消息
     */
    private SubmitSmResp handleUdhSegmentedMessage(SubmitSm submitSm, SmppUdhInfo udhInfo) {
        log.debug("处理UDH分段消息 - systemId: {}, 参考号: {}, 分段: {}/{}",
                account.getSystemId(), udhInfo.getRefNum(),
                udhInfo.getPartNum(), udhInfo.getTotalParts());

        // 处理分段重组
        SegmentReassembleInfo reassembled =
            segmentManager.processUdhSegment(submitSm, account.getSystemId(), udhInfo);

        if (reassembled != null) {
            // 重组完成，提交完整消息
            return handleCompleteMessage(reassembled.getOriginalSubmitSm(), reassembled.getCompleteMessage());
        } else {
            // 分段缓存中，返回成功响应
            return createSuccessResponse(submitSm, generateMessageId());
        }
    }

    /**
     * 处理完整消息
     */
    private SubmitSmResp handleCompleteMessage(SubmitSm submitSm, String message) {
        try {
            // 调用业务服务提交消息
            String messageId = submitProcessor.submitMessage(submitSm, message, account);

            if (messageId != null) {
                log.info("消息提交成功 - systemId: {}, messageId: {}, 长度: {}",
                        account.getSystemId(), messageId, message.length());
                return createSuccessResponse(submitSm, messageId);
            } else {
                log.warn("消息提交失败 - systemId: {}", account.getSystemId());
                return createErrorResponse(submitSm, SmppConstants.STATUS_SUBMITFAIL);
            }

        } catch (Exception e) {
            log.error("提交消息异常 - systemId: {}", account.getSystemId(), e);
            return createErrorResponse(submitSm, SmppConstants.STATUS_SYSERR);
        }
    }

    // ==================== 解析方法 ====================

    /**
     * 检查是否有SAR TLV标签
     */
    private boolean hasSarTlvTags(SubmitSm submitSm) {
        return submitSm.hasOptionalParameter(SmppConstants.TAG_SAR_MSG_REF_NUM)
            && submitSm.hasOptionalParameter(SmppConstants.TAG_SAR_TOTAL_SEGMENTS)
            && submitSm.hasOptionalParameter(SmppConstants.TAG_SAR_SEGMENT_SEQNUM);
    }

    /**
     * 解析SAR信息
     */
    private SmppSarInfo parseSarInfo(SubmitSm submitSm) {
        try {
            Tlv refNumTlv = submitSm.getOptionalParameter(SmppConstants.TAG_SAR_MSG_REF_NUM);
            Tlv totalSegTlv = submitSm.getOptionalParameter(SmppConstants.TAG_SAR_TOTAL_SEGMENTS);
            Tlv segSeqTlv = submitSm.getOptionalParameter(SmppConstants.TAG_SAR_SEGMENT_SEQNUM);

            if (refNumTlv == null || totalSegTlv == null || segSeqTlv == null) {
                return null;
            }

            SmppSarInfo sarInfo = new SmppSarInfo();
            sarInfo.setRefNum(getSarTlvValue(refNumTlv));
            sarInfo.setTotalSegments(getSarTlvValue(totalSegTlv));
            sarInfo.setSegmentSeq(getSarTlvValue(segSeqTlv));

            return sarInfo;

        } catch (Exception e) {
            log.error("解析SAR信息异常", e);
            return null;
        }
    }

    /**
     * 解析UDH信息
     */
    private SmppUdhInfo parseUdhInfo(SubmitSm submitSm) {
        byte[] shortMessage = submitSm.getShortMessage();
        if (shortMessage == null || shortMessage.length < 6) {
            return null;
        }

        try {
            int offset = 0;
            int udhl = shortMessage[offset++] & 0xFF;

            if (shortMessage.length < udhl + 1) {
                return null;
            }

            SmppUdhInfo udhInfo = new SmppUdhInfo();
            int udhEnd = offset + udhl;

            while (offset < udhEnd) {
                int iei = shortMessage[offset++] & 0xFF;
                if (offset >= udhEnd) break;

                int iedl = shortMessage[offset++] & 0xFF;
                if (offset + iedl > udhEnd) break;

                if (iei == 0x00 && iedl == 3) {
                    udhInfo.setRefNum(shortMessage[offset] & 0xFF);
                    udhInfo.setTotalParts(shortMessage[offset + 1] & 0xFF);
                    udhInfo.setPartNum(shortMessage[offset + 2] & 0xFF);
                } else if (iei == 0x08 && iedl == 4) {
                    udhInfo.setRefNum(((shortMessage[offset] & 0xFF) << 8) | (shortMessage[offset + 1] & 0xFF));
                    udhInfo.setTotalParts(shortMessage[offset + 2] & 0xFF);
                    udhInfo.setPartNum(shortMessage[offset + 3] & 0xFF);
                }

                offset += iedl;
            }

            if (udhInfo.getRefNum() > 0 && udhInfo.getTotalParts() > 0 && udhInfo.getPartNum() > 0) {
                int userDataLength = shortMessage.length - udhl - 1;
                udhInfo.setUserData(new byte[userDataLength]);
                System.arraycopy(shortMessage, udhl + 1, udhInfo.getUserData(), 0, userDataLength);
                return udhInfo;
            }

        } catch (Exception e) {
            log.error("解析UDH信息异常", e);
        }

        return null;
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取数据编码
     */
    private byte getDataCoding() {
        return encodingConverter.convertToDataCoding(account.getEncoding());
    }

    /**
     * 获取SAR TLV值
     */
    private int getSarTlvValue(Tlv tlv) {
        byte[] value = tlv.getValue();
        if (value.length == 1) {
            return value[0] & 0xFF;
        } else if (value.length == 2) {
            return ((value[0] & 0xFF) << 8) | (value[1] & 0xFF);
        }
        return 0;
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "MSG_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 创建成功响应
     */
    private SubmitSmResp createSuccessResponse(SubmitSm submitSm, String messageId) {
        SubmitSmResp response = submitSm.createResponse();
        response.setMessageId(messageId);
        response.setCommandStatus(SmppConstants.STATUS_OK);
        return response;
    }

    /**
     * 创建错误响应
     */
    private SubmitSmResp createErrorResponse(SubmitSm submitSm, int status) {
        SubmitSmResp response = submitSm.createResponse();
        response.setCommandStatus(status);
        return response;
    }
}
