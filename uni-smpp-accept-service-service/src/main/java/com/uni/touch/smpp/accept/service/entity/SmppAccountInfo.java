package com.uni.touch.smpp.accept.service.entity;

import com.uni.touch.user.api.response.AppInfoResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SMPP账号信息
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmppAccountInfo {

    /**
     * 系统ID
     */
    private String systemId;

    /**
     * 密码
     */
    private String password;

    /**
     * 编码类型
     */
    private Integer encoding;

    /**
     * 最大连接数（对应smppMaxLink）
     */
    private Integer maxConnections;

    /**
     * 窗口大小（对应smppMaxSlippingWindowNumber）
     */
    private Integer windowSize;

    /**
     * 单连接最大提交速度
     */
    private Integer maxSubmitSpeed;

    /**
     * 单连接最大状态报告速度
     */
    private Integer maxDlrSpeed;

    /**
     * 从AppInfoResponse转换
     */
    public static SmppAccountInfo fromAppInfo(AppInfoResponse appInfo) {
        return SmppAccountInfo.builder()
                .systemId(appInfo.getSmppSystemId())
                .password(appInfo.getSmppPassword())
                .encoding(appInfo.getSmppEncoding())
                .maxConnections(appInfo.getSmppMaxLink())
                .windowSize(appInfo.getSmppMaxSlippingWindowNumber())
                .maxSubmitSpeed(appInfo.getSmppMaxSubmitSpeedPerLink())
                .maxDlrSpeed(appInfo.getSmppMaxDlrSpeedPerLink())
                .build();
    }
}
