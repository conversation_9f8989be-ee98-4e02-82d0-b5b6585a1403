package com.uni.touch.smpp.accept.service.converter;

import com.cloudhopper.commons.charset.Charset;
import com.cloudhopper.commons.charset.CharsetUtil;
import com.cloudhopper.smpp.SmppConstants;
import com.uni.touch.smpp.accept.common.enums.AppSmppEncodingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * SMPP编码转换器
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Component
public class SmppEncodingConverter {

    /**
     * 将服务端encoding转换为SMPP标准data_coding
     */
    public byte toDataCoding(Integer encoding) {
        AppSmppEncodingEnum encodingEnum = getEncodingEnum(encoding);

        switch (encodingEnum) {
            case GSM_7:
                return SmppConstants.DATA_CODING_DEFAULT;  // GSM 7-bit (0x00)
            case ASCII:
                return SmppConstants.DATA_CODING_IA5;      // ASCII/IA5 (0x01)
            case LATIN_1:
                return SmppConstants.DATA_CODING_LATIN1;   // Latin-1/ISO-8859-1 (0x03)
            default:
                log.warn("不支持的编码配置: {}, 使用默认GSM编码", encoding);
                return SmppConstants.DATA_CODING_DEFAULT;
        }
    }

    /**
     * 获取编码对应的字符集
     */
    public Charset getCharset(Integer encoding) {
        AppSmppEncodingEnum encodingEnum = getEncodingEnum(encoding);

        switch (encodingEnum) {
            case GSM_7:
                return CharsetUtil.CHARSET_GSM;        // GSM 7-bit
            case ASCII:
            case LATIN_1:
                return CharsetUtil.CHARSET_ISO_8859_1; // ASCII/Latin-1 都使用ISO-8859-1
            default:
                log.warn("不支持的编码配置: {}, 使用默认GSM字符集", encoding);
                return CharsetUtil.CHARSET_GSM;
        }
    }

    /**
     * 获取编码枚举，如果无效则返回默认GSM编码
     */
    private AppSmppEncodingEnum getEncodingEnum(Integer encoding) {
        if (encoding == null) {
            log.warn("编码配置为空，使用默认GSM编码");
            return AppSmppEncodingEnum.GSM_7;
        }

        return AppSmppEncodingEnum.fromValue(encoding)
                .orElseGet(() -> {
                    log.warn("不支持的编码配置: {}, 使用默认GSM编码", encoding);
                    return AppSmppEncodingEnum.GSM_7;
                });
    }

    /**
     * 解码消息（统一使用服务端encoding）
     */
    public String decode(byte[] messageBytes, Integer encoding) {
        if (messageBytes == null || messageBytes.length == 0) {
            return "";
        }

        AppSmppEncodingEnum encodingEnum = getEncodingEnum(encoding);
        Charset charset = getCharset(encoding);

        try {
            String decodedContent = CharsetUtil.decode(messageBytes, charset);

            log.info("消息解码 - 编码: {} ({}), 字节长度: {}, 字节内容: {}, 解码结果: '{}'",
                    encodingEnum.getValue(), encodingEnum.getDesc(),
                    messageBytes.length, bytesToHexString(messageBytes), decodedContent);

            return decodedContent;
        } catch (Exception e) {
            log.error("解码消息异常 - 编码: {} ({}), 字节长度: {}, 字节内容: {}",
                    encodingEnum.getValue(), encodingEnum.getDesc(),
                    messageBytes.length, bytesToHexString(messageBytes), e);

            // 降级使用GSM编码
            String fallbackContent = CharsetUtil.decode(messageBytes, CharsetUtil.CHARSET_GSM);
            log.warn("降级使用GSM编码解码结果: '{}'", fallbackContent);
            return fallbackContent;
        }
    }

    /**
     * 编码消息（统一使用服务端encoding）
     */
    public byte[] encode(String content, Integer encoding) {
        if (content == null || content.isEmpty()) {
            return new byte[0];
        }

        AppSmppEncodingEnum encodingEnum = getEncodingEnum(encoding);
        Charset charset = getCharset(encoding);

        try {
            byte[] encodedBytes = CharsetUtil.encode(content, charset);

            log.info("消息编码 - 编码: {} ({}), 内容: '{}', 字节长度: {}, 字节内容: {}",
                    encodingEnum.getValue(), encodingEnum.getDesc(), content,
                    encodedBytes.length, bytesToHexString(encodedBytes));

            return encodedBytes;
        } catch (Exception e) {
            log.error("编码消息异常 - 编码: {} ({}), 内容: '{}'",
                    encodingEnum.getValue(), encodingEnum.getDesc(), content, e);

            // 降级使用GSM编码
            byte[] fallbackBytes = CharsetUtil.encode(content, CharsetUtil.CHARSET_GSM);
            log.warn("降级使用GSM编码结果 - 字节长度: {}, 字节内容: {}",
                    fallbackBytes.length, bytesToHexString(fallbackBytes));
            return fallbackBytes;
        }
    }

    /**
     * 校验客户端data_coding是否匹配服务端encoding
     */
    public boolean validateDataCoding(byte clientDataCoding, Integer serverEncoding) {
        byte expectedDataCoding = toDataCoding(serverEncoding);
        boolean isValid = clientDataCoding == expectedDataCoding;

        AppSmppEncodingEnum encodingEnum = getEncodingEnum(serverEncoding);
        log.info("编码校验 - 客户端data_coding: 0x{}, 服务端encoding: {} ({}), 期望data_coding: 0x{}, 校验结果: {}",
                Integer.toHexString(clientDataCoding),
                encodingEnum.getValue(), encodingEnum.getDesc(),
                Integer.toHexString(expectedDataCoding),
                isValid ? "通过" : "失败");

        return isValid;
    }

    /**
     * 获取单条短信最大长度
     * 所有支持的编码都是160字符
     */
    public int getMaxSingleSmsLength(Integer encoding) {
        AppSmppEncodingEnum encodingEnum = getEncodingEnum(encoding);

        // 所有支持的编码（GSM-7, ASCII, Latin-1）都是160字符
        log.debug("获取最大短信长度 - 编码: {} ({}), 最大长度: 160",
                encodingEnum.getValue(), encodingEnum.getDesc());
        return 160;
    }

    /**
     * 检查消息是否为长短信
     */
    public boolean isLongMessage(String content, Integer encoding) {
        if (content == null) {
            return false;
        }

        int maxLength = getMaxSingleSmsLength(encoding);
        boolean isLong = content.length() > maxLength;

        AppSmppEncodingEnum encodingEnum = getEncodingEnum(encoding);
        log.debug("长短信检查 - 编码: {} ({}), 内容长度: {}, 最大长度: {}, 是否长短信: {}",
                encodingEnum.getValue(), encodingEnum.getDesc(),
                content.length(), maxLength, isLong);

        return isLong;
    }

    /**
     * 字节数组转十六进制字符串（用于日志）
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(bytes.length, 50); i++) { // 最多显示50个字节
            sb.append(String.format("%02X ", bytes[i]));
        }
        if (bytes.length > 50) {
            sb.append("...(").append(bytes.length).append(" bytes total)");
        }
        return sb.toString().trim();
    }
}
