package com.uni.touch.smpp.accept.service.processor;

import com.cloudhopper.smpp.SmppConstants;
import com.cloudhopper.smpp.pdu.SubmitSm;
import com.uni.touch.smpp.accept.service.converter.SmppEncodingConverter;
import com.uni.touch.smpp.accept.service.entity.SegmentReassembleInfo;
import com.uni.touch.smpp.accept.service.entity.SegmentMessageGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分段消息重组服务
 * 专门负责分段消息的重组逻辑
 * 
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
@Service
public class SmppSegmentProcessor {

    @Autowired
    private SmppEncodingConverter encodingConverter;

    /**
     * 重组分段消息
     * 
     * @param segmentMessageGroup 分段组
     * @return 重组后的完整消息，如果重组失败返回null
     */
    public SegmentReassembleInfo reassemble(SegmentMessageGroup segmentMessageGroup) {
        if (segmentMessageGroup == null || !segmentMessageGroup.isComplete()) {
            log.warn("分段组不完整，无法重组 - key: {}, 已收集: {}/{}", 
                    segmentMessageGroup != null ? segmentMessageGroup.getCacheKey() : "null",
                    segmentMessageGroup != null ? segmentMessageGroup.getCollectedSegmentCount() : 0,
                    segmentMessageGroup != null ? segmentMessageGroup.getTotalSegments() : 0);
            return null;
        }

        try {
            StringBuilder completeMessage = new StringBuilder();
            SubmitSm firstSegment = segmentMessageGroup.getFirstSegment();
            
            if (firstSegment == null) {
                log.error("缺失第一个分段 - key: {}", segmentMessageGroup.getCacheKey());
                return null;
            }

            // 按序号重组消息
            for (int i = 1; i <= segmentMessageGroup.getTotalSegments(); i++) {
                SubmitSm segment = segmentMessageGroup.getSegments().get(i);
                if (segment == null) {
                    throw new IllegalStateException("缺失分段: " + i + ", key: " + segmentMessageGroup.getCacheKey());
                }
                
                // 解码并拼接消息内容
                String segmentText = decodeSegmentMessage(segment, firstSegment);
                if (segmentText != null) {
                    completeMessage.append(segmentText);
                }
            }

            log.info("分段消息重组成功 - key: {}, 类型: {}, 总分段: {}, 完整消息长度: {}", 
                    segmentMessageGroup.getCacheKey(), segmentMessageGroup.getType(),
                    segmentMessageGroup.getTotalSegments(), completeMessage.length());

            return new SegmentReassembleInfo(firstSegment, completeMessage.toString(),
                                        segmentMessageGroup.getRefNum(), segmentMessageGroup.getTotalSegments(),
                                        segmentMessageGroup.getType());
                                        
        } catch (Exception e) {
            log.error("重组分段消息异常 - key: {}", segmentMessageGroup.getCacheKey(), e);
            return null;
        }
    }

    /**
     * 解码单个分段的消息内容
     * 
     * @param segment 当前分段
     * @param firstSegment 第一个分段（用于获取编码信息）
     * @return 解码后的文本内容
     */
    private String decodeSegmentMessage(SubmitSm segment, SubmitSm firstSegment) {
        byte[] messageBytes = segment.getShortMessage();
        if (messageBytes == null || messageBytes.length == 0) {
            return "";
        }

        try {
            // 使用第一个分段的编码信息进行解码
            Integer smppEncoding = convertDataCodingToSmppEncoding(firstSegment.getDataCoding());
            return encodingConverter.decodeMessage(messageBytes, smppEncoding);
        } catch (Exception e) {
            log.error("解码分段消息异常 - dataCoding: 0x{}", 
                    Integer.toHexString(firstSegment.getDataCoding()), e);
            // 降级处理：使用默认编码
            return encodingConverter.decodeMessage(messageBytes, 1);
        }
    }

    /**
     * 将dataCoding转换为smppEncoding
     * 这个方法从SegmentGroup中移出来，作为重组服务的一部分
     * 
     * @param dataCoding SMPP协议的dataCoding
     * @return smppEncoding值
     */
    private Integer convertDataCodingToSmppEncoding(byte dataCoding) {
        switch (dataCoding) {
            case SmppConstants.DATA_CODING_DEFAULT:
                return 1; // GSM 7-bit
            case SmppConstants.DATA_CODING_IA5:
                return 2; // ASCII/IA5
            case SmppConstants.DATA_CODING_LATIN1:
                return 3; // Latin-1/ISO-8859-1
            case SmppConstants.DATA_CODING_UCS2:
                return 4; // UCS2
            case SmppConstants.DATA_CODING_8BIT:
                return 3; // 8-bit binary -> Latin-1
            default:
                log.warn("未知的dataCoding: 0x{}, 使用默认GSM编码", Integer.toHexString(dataCoding));
                return 1; // 默认GSM 7-bit
        }
    }

    /**
     * 验证分段组的完整性
     * 
     * @param segmentMessageGroup 分段组
     * @return 验证结果
     */
    public boolean validateSegmentGroup(SegmentMessageGroup segmentMessageGroup) {
        if (segmentMessageGroup == null) {
            return false;
        }

        // 检查是否完整
        if (!segmentMessageGroup.isComplete()) {
            return false;
        }

        // 检查所有分段是否存在
        for (int i = 1; i <= segmentMessageGroup.getTotalSegments(); i++) {
            if (!segmentMessageGroup.hasSegment(i)) {
                log.error("分段组验证失败：缺失分段 {} - key: {}", i, segmentMessageGroup.getCacheKey());
                return false;
            }
        }

        return true;
    }
}
