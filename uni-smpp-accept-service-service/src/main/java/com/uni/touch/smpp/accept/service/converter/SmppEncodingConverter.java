package com.uni.touch.smpp.accept.service.converter;

import com.cloudhopper.commons.charset.Charset;
import com.cloudhopper.commons.charset.CharsetUtil;
import com.cloudhopper.smpp.SmppConstants;
import com.uni.touch.smpp.accept.common.enums.AppSmppEncodingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 简化的SMPP编码转换器
 * 只支持配置的3种编码：1-GSM-7, 2-ASCII, 3-Latin-1
 * 统一使用服务端编码配置，约束客户端必须遵循
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@Component
public class SmppEncodingConverter {

    /**
     * 将服务端encoding转换为SMPP标准data_coding
     * 只支持1-GSM-7, 2-ASCII, 3-Latin-1
     */
    public byte toDataCoding(Integer encoding) {
        if (encoding == null) {
            log.warn("编码配置为空，使用默认GSM编码");
            return SmppConstants.DATA_CODING_DEFAULT;
        }

        switch (encoding) {
            case 1:
                return SmppConstants.DATA_CODING_DEFAULT;  // GSM 7-bit (0x00)
            case 2:
                return SmppConstants.DATA_CODING_IA5;      // ASCII/IA5 (0x01)
            case 3:
                return SmppConstants.DATA_CODING_LATIN1;   // Latin-1/ISO-8859-1 (0x03)
            default:
                log.warn("不支持的编码配置: {}, 使用默认GSM编码", encoding);
                return SmppConstants.DATA_CODING_DEFAULT;
        }
    }

    /**
     * 获取编码对应的字符集
     * 只支持1-GSM-7, 2-ASCII, 3-Latin-1
     */
    public Charset getCharset(Integer encoding) {
        if (encoding == null) {
            log.warn("编码配置为空，使用默认GSM字符集");
            return CharsetUtil.CHARSET_GSM;
        }

        switch (encoding) {
            case 1:
                return CharsetUtil.CHARSET_GSM;        // GSM 7-bit
            case 2:
                return CharsetUtil.CHARSET_ISO_8859_1; // ASCII/IA5 (ISO-8859-1)
            case 3:
                return CharsetUtil.CHARSET_ISO_8859_1; // Latin-1 (ISO-8859-1)
            default:
                log.warn("不支持的编码配置: {}, 使用默认GSM字符集", encoding);
                return CharsetUtil.CHARSET_GSM;
        }
    }

    /**
     * 解码消息（统一使用服务端encoding）
     * 包含详细的日志记录用于链路追踪
     */
    public String decode(byte[] messageBytes, Integer encoding) {
        if (messageBytes == null || messageBytes.length == 0) {
            log.debug("消息字节为空，返回空字符串");
            return "";
        }

        try {
            Charset charset = getCharset(encoding);
            String decodedContent = CharsetUtil.decode(messageBytes, charset);

            // 详细日志记录
            log.info("消息解码 - 编码: {}, 字节长度: {}, 字节内容: {}, 解码结果: '{}'",
                    getEncodingDescription(encoding),
                    messageBytes.length,
                    bytesToHexString(messageBytes),
                    decodedContent);

            return decodedContent;
        } catch (Exception e) {
            log.error("解码消息异常 - 编码: {}, 字节长度: {}, 字节内容: {}",
                    encoding, messageBytes.length, bytesToHexString(messageBytes), e);
            // 降级使用GSM编码
            String fallbackContent = CharsetUtil.decode(messageBytes, CharsetUtil.CHARSET_GSM);
            log.warn("降级使用GSM编码解码结果: '{}'", fallbackContent);
            return fallbackContent;
        }
    }

    /**
     * 编码消息（统一使用服务端encoding）
     * 包含详细的日志记录用于链路追踪
     */
    public byte[] encode(String content, Integer encoding) {
        if (content == null || content.isEmpty()) {
            log.debug("消息内容为空，返回空字节数组");
            return new byte[0];
        }

        try {
            Charset charset = getCharset(encoding);
            byte[] encodedBytes = CharsetUtil.encode(content, charset);

            // 详细日志记录
            log.info("消息编码 - 编码: {}, 内容: '{}', 字节长度: {}, 字节内容: {}",
                    getEncodingDescription(encoding),
                    content,
                    encodedBytes.length,
                    bytesToHexString(encodedBytes));

            return encodedBytes;
        } catch (Exception e) {
            log.error("编码消息异常 - 编码: {}, 内容: '{}'", encoding, content, e);
            // 降级使用GSM编码
            byte[] fallbackBytes = CharsetUtil.encode(content, CharsetUtil.CHARSET_GSM);
            log.warn("降级使用GSM编码结果 - 字节长度: {}, 字节内容: {}",
                    fallbackBytes.length, bytesToHexString(fallbackBytes));
            return fallbackBytes;
        }
    }

    /**
     * 校验客户端data_coding是否匹配服务端encoding
     */
    public boolean validateDataCoding(byte clientDataCoding, Integer serverEncoding) {
        byte expectedDataCoding = toDataCoding(serverEncoding);
        boolean isValid = clientDataCoding == expectedDataCoding;

        log.info("编码校验 - 客户端data_coding: 0x{}, 服务端encoding: {}, 期望data_coding: 0x{}, 校验结果: {}",
                Integer.toHexString(clientDataCoding),
                serverEncoding,
                Integer.toHexString(expectedDataCoding),
                isValid ? "通过" : "失败");

        return isValid;
    }

    /**
     * 获取单条短信最大长度
     * 只支持1-GSM-7, 2-ASCII, 3-Latin-1
     */
    public int getMaxSingleSmsLength(Integer encoding) {
        if (encoding == null) {
            log.warn("编码配置为空，使用默认长度160");
            return 160;
        }

        switch (encoding) {
            case 1: return 160; // GSM 7-bit
            case 2: return 160; // ASCII
            case 3: return 160; // Latin-1
            default:
                log.warn("不支持的编码配置: {}, 使用默认长度160", encoding);
                return 160;
        }
    }

    /**
     * 获取编码类型描述
     * 只支持1-GSM-7, 2-ASCII, 3-Latin-1
     */
    public String getEncodingDescription(Integer encoding) {
        if (encoding == null) {
            return "GSM 7-bit (默认)";
        }

        switch (encoding) {
            case 1: return "GSM 7-bit";
            case 2: return "ASCII (IA5)";
            case 3: return "Latin-1 (ISO-8859-1)";
            default: return "未知编码(" + encoding + ")";
        }
    }

    /**
     * 检查消息是否为长短信
     */
    public boolean isLongMessage(String content, Integer encoding) {
        if (content == null) {
            return false;
        }

        int maxLength = getMaxSingleSmsLength(encoding);
        boolean isLong = content.length() > maxLength;

        log.debug("长短信检查 - 编码: {}, 内容长度: {}, 最大长度: {}, 是否长短信: {}",
                getEncodingDescription(encoding), content.length(), maxLength, isLong);

        return isLong;
    }

    /**
     * 字节数组转十六进制字符串（用于日志）
     */
    private String bytesToHexString(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(bytes.length, 50); i++) { // 最多显示50个字节
            sb.append(String.format("%02X ", bytes[i]));
        }
        if (bytes.length > 50) {
            sb.append("...(").append(bytes.length).append(" bytes total)");
        }
        return sb.toString().trim();
    }

    /**
     * 获取支持的编码类型
     * 只返回配置支持的3种编码
     */
    public String[] getSupportedEncodings() {
        return new String[]{
            "1 - GSM 7-bit",
            "2 - ASCII (IA5)",
            "3 - Latin-1 (ISO-8859-1)"
        };
    }
}
