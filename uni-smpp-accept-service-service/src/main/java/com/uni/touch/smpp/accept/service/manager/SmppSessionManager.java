package com.uni.touch.smpp.accept.service.manager;

import com.cloudhopper.smpp.SmppServerSession;
import com.uni.touch.smpp.accept.service.entity.SmppAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * SMPP会话管理器
 * 基于ch-smpp框架，专注于会话管理和连接计数
 *
 * <AUTHOR>
 * @date 2025/07/27
 */
@Slf4j
@Component
public class SmppSessionManager {

    /**
     * 账号连接计数器 - systemId -> AtomicInteger
     */
    private final Map<String, AtomicInteger> connectionCounters = new ConcurrentHashMap<>();

    /**
     * 会话映射 - sessionId -> SmppServerSession
     */
    private final Map<Long, SmppServerSession> sessionMap = new ConcurrentHashMap<>();

    /**
     * 账号配置映射 - systemId -> SmppAccount
     */
    private final Map<String, SmppAccountInfo> accountMap = new ConcurrentHashMap<>();

    /**
     * 尝试获取连接许可（基于smppMaxLink）
     */
    public boolean tryAcquireConnection(String systemId, Integer maxConnections) {
        if (maxConnections == null || maxConnections <= 0) {
            maxConnections = 1;
        }

        AtomicInteger counter = connectionCounters.computeIfAbsent(systemId,
                k -> new AtomicInteger(0));

        while (true) {
            int current = counter.get();
            if (current >= maxConnections) {
                log.warn("连接数已达上限 - systemId: {}, current: {}, max: {}",
                        systemId, current, maxConnections);
                return false;
            }
            if (counter.compareAndSet(current, current + 1)) {
                log.info("获取连接许可成功 - systemId: {}, current: {}, max: {}",
                        systemId, current + 1, maxConnections);
                return true;
            }
        }
    }

    /**
     * 释放连接许可
     */
    public void releaseConnection(String systemId) {
        AtomicInteger counter = connectionCounters.get(systemId);
        if (counter != null) {
            int newCount = counter.decrementAndGet();
            log.info("释放连接许可 - systemId: {}, remaining: {}", systemId, Math.max(0, newCount));

            if (newCount < 0) {
                counter.set(0);
                log.warn("连接计数异常，已重置为0 - systemId: {}", systemId);
            }
        }
    }

    /**
     * 注册会话
     */
    public void registerSession(Long sessionId, SmppServerSession session, SmppAccountInfo account) {
        sessionMap.put(sessionId, session);
        accountMap.put(account.getSystemId(), account);
        log.info("注册SMPP会话 - sessionId: {}, systemId: {}",
                sessionId, session.getConfiguration().getSystemId());
    }

    /**
     * 移除会话
     */
    public void removeSession(Long sessionId) {
        SmppServerSession session = sessionMap.remove(sessionId);
        if (session != null) {
            String systemId = session.getConfiguration().getSystemId();
            releaseConnection(systemId);
            log.info("移除SMPP会话 - sessionId: {}, systemId: {}", sessionId, systemId);
        }
    }

    /**
     * 获取当前连接数
     */
    public int getCurrentConnectionCount(String systemId) {
        AtomicInteger counter = connectionCounters.get(systemId);
        return counter != null ? counter.get() : 0;
    }

    /**
     * 获取会话
     */
    public SmppServerSession getSession(Long sessionId) {
        return sessionMap.get(sessionId);
    }

    /**
     * 定期校正连接计数（防止计数漂移）
     */
    @Scheduled(fixedRate = 60000)
    public void correctConnectionCounts() {
        log.debug("开始校正连接计数...");

        for (String systemId : connectionCounters.keySet()) {
            try {
                correctConnectionCount(systemId);
            } catch (Exception e) {
                log.error("校正连接计数失败 - systemId: {}", systemId, e);
            }
        }

        log.debug("连接计数校正完成");
    }

    /**
     * 校正单个账号的连接计数
     */
    private void correctConnectionCount(String systemId) {
        // 统计实际活跃会话数
        long actualCount = sessionMap.values().stream()
                .filter(session -> systemId.equals(session.getConfiguration().getSystemId()))
                .filter(session -> session.isBound())
                .count();

        AtomicInteger counter = connectionCounters.get(systemId);
        if (counter != null) {
            int currentCount = counter.get();
            if (currentCount != actualCount) {
                counter.set((int) actualCount);
                log.warn("连接计数校正 - systemId: {}, 校正前: {}, 校正后: {}",
                        systemId, currentCount, actualCount);
            }
        } else if (actualCount > 0) {
            connectionCounters.put(systemId, new AtomicInteger((int) actualCount));
            log.warn("创建缺失的连接计数器 - systemId: {}, count: {}", systemId, actualCount);
        }
    }

    /**
     * 根据systemId查找会话
     */
    public SmppServerSession findSessionBySystemId(String systemId) {
        return sessionMap.values().stream()
                .filter(session -> systemId.equals(session.getConfiguration().getSystemId()))
                .filter(SmppServerSession::isBound)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据systemId获取账号配置
     */
    public SmppAccountInfo getAccountBySystemId(String systemId) {
        return accountMap.get(systemId);
    }
}
